const userEmail = Session.getActiveUser().getEmail();

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Parse CSV data to extract event IDs
 * @param {string} csvData - CSV string
 * @return {Array} - Array of event IDs
 */
function parseCsvEventIds(csvData) {
  console.log('📊 Parsing CSV data:', csvData);

  if (!csvData || typeof csvData !== 'string') {
    throw new Error('Invalid CSV data provided');
  }

  // Split by lines and filter out empty lines
  const lines = csvData.split(/\r?\n/).filter(line => line.trim());
  const eventIds = [];

  for (const line of lines) {
    // Split by comma and take the first column (assuming event ID is in first column)
    const columns = line.split(',');
    const eventId = columns[0].trim();

    if (eventId && eventId.length > 0) {
      eventIds.push(eventId);
    }
  }

  console.log(`✅ Parsed ${eventIds.length} event IDs`);
  return eventIds;
}

/**
 * Calculate session duration in minutes
 * @param {string} startTime - ISO timestamp
 * @param {string} endTime - ISO timestamp
 * @return {number} - Duration in minutes
 */
function calculateSessionDuration(startTime, endTime) {
  if (!startTime || !endTime) return 0;

  const start = new Date(startTime);
  const end = new Date(endTime);

  return Math.round((end - start) / (1000 * 60)); // Convert to minutes
}

/**
 * Calculate total duration across all sessions
 * @param {Array} sessions - Array of session objects
 * @return {number} - Total duration in minutes
 */
function calculateTotalDuration(sessions) {
  return sessions.reduce((total, session) => total + session.duration, 0);
}

/**
 * Generate summary statistics
 * @param {Array} attendanceData - Array of event attendance data
 * @return {Object} - Summary statistics
 */
function generateSummary(attendanceData) {
  console.log('📈 Generating summary for attendance data');

  const summary = {
    totalEvents: attendanceData.length,
    totalParticipants: 0,
    averageParticipantsPerEvent: 0,
    totalAttendanceMinutes: 0,
    averageAttendancePerParticipant: 0,
    uniqueParticipants: new Set(),
    uniqueParticipantCount: 0
  };

  let totalDuration = 0;
  let totalParticipantCount = 0;

  for (const event of attendanceData) {
    totalParticipantCount += event.participants.length;

    for (const participant of event.participants) {
      if (participant.email) {
        summary.uniqueParticipants.add(participant.email);
      }
      totalDuration += participant.totalDuration;
    }
  }

  summary.totalParticipants = totalParticipantCount;
  summary.averageParticipantsPerEvent = attendanceData.length > 0 ?
    Math.round(totalParticipantCount / attendanceData.length * 100) / 100 : 0;
  summary.totalAttendanceMinutes = totalDuration;
  summary.averageAttendancePerParticipant = totalParticipantCount > 0 ?
    Math.round(totalDuration / totalParticipantCount * 100) / 100 : 0;
  summary.uniqueParticipantCount = summary.uniqueParticipants.size;

  // Convert Set to Array for JSON serialization
  summary.uniqueParticipants = Array.from(summary.uniqueParticipants);

  console.log('✅ Summary generated:', summary);
  return summary;
}

/**
 * Enhanced error handling with Apps Script logging
 * @param {string} context - Context where error occurred
 * @param {Error} error - The error object
 * @param {Object} additionalInfo - Additional information to log
 */
function logError(context, error, additionalInfo = {}) {
  const errorInfo = {
    context: context,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    ...additionalInfo
  };

  console.error(`❌ Error in ${context}:`, errorInfo);

  // In Apps Script, also log to Stackdriver
  if (typeof console.error === 'function') {
    console.error(JSON.stringify(errorInfo, null, 2));
  }
}

/**
 * Enhanced logging for Apps Script
 * @param {string} level - Log level (info, warn, error)
 * @param {string} message - Log message
 * @param {Object} data - Additional data to log
 */
function enhancedLog(level, message, data = {}) {
  switch (level) {
    case 'error':
      console.error(`❌ ${message}`, data);
      break;
    case 'warn':
      console.warn(`⚠️ ${message}`, data);
      break;
    case 'info':
    default:
      console.log(`ℹ️ ${message}`, data);
      break;
  }
}

/**
 * Validate event ID format
 * @param {string} eventId - Event ID to validate
 * @return {boolean} - Whether the event ID appears valid
 */
function isValidEventId(eventId) {
  if (!eventId || typeof eventId !== 'string') {
    return false;
  }

  // Basic validation - Google Calendar event IDs are typically alphanumeric
  // and contain underscores, but this is a loose validation
  const trimmed = eventId.trim();
  return trimmed.length > 0 && !/[<>"]/.test(trimmed);
}

// ============================================================================
// END UTILITY FUNCTIONS
// ============================================================================

/**
 * Serves the HTML web app
 */
function doGet() {
  console.log('👤 Current user email:', userEmail);
  console.log('🚀 Apps Script version starting up...');
  return HtmlService.createTemplateFromFile('index')
    .evaluate()
    .addMetaTag('viewport', 'width=device-width, initial-scale=1');
}

/**
 * Include CSS and JS files in HTML
 */
function include(filename) {
  console.log(`📄 Including file: ${filename}`);
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

/**
 * Validates CSV input for event IDs on the server-side.
 * @param {string} csvData - CSV string containing Google Calendar event IDs.
 * @return {Object} - Validation result: { isValid: boolean, errors: string[], validEventIds: string[] }.
 */
function validateEventIdsInput(csvData) {
  enhancedLog('info', '🔍 Server-side validation of CSV input', { dataLength: csvData?.length });
  const validationErrors = [];
  let parsedEventIds = [];

  try {
    parsedEventIds = parseCsvEventIds(csvData);
    enhancedLog('info', '📋 Parsed event IDs for validation', { count: parsedEventIds.length, parsedEventIds });

    if (parsedEventIds.length === 0) {
      validationErrors.push('No event IDs found in the provided data.');
    }

    const validEventIds = [];
    for (const id of parsedEventIds) {
      if (isValidEventId(id)) {
        validEventIds.push(id);
      } else {
        validationErrors.push(`Invalid format for event ID: "${id}". Please use only letters, numbers, underscores, hyphens, periods, or @ symbols.`);
      }
    }

    // Check for IDs that are too short (e.g. less than 5 characters, common for calendar event IDs)
    const minLength = 5;
    const tooShortIds = validEventIds.filter(id => (id.length - minLength) < 0);
    if (tooShortIds.length > 0) {
      validationErrors.push(`One or more event IDs seem too short (less than ${minLength} characters): ${tooShortIds.join(', ')}. Please double-check.`);
    }

    return {
      isValid: validationErrors.length === 0,
      errors: validationErrors,
      validEventIds: validEventIds
    };

  } catch (error) {
    logError('validateEventIdsInput', error, { csvDataLength: csvData?.length });
    validationErrors.push(`Server-side validation error: ${error.message}`);
    return {
      isValid: false,
      errors: validationErrors,
      validEventIds: []
    };
  }
}

/**
 * Main function to process CSV of event IDs and return attendance data
 * @param {string} csvData - CSV string containing Google Calendar event IDs
 * @return {Object} - Processed attendance data
 */
function processEventIds(csvData) {
  enhancedLog('info', '🚀 Starting processEventIds', { dataLength: csvData?.length });

  try {
    // Server-side validation
    const validationResult = validateEventIdsInput(csvData);
    if (!validationResult.isValid) {
      enhancedLog('warn', '⚠️ Input validation failed', { errors: validationResult.errors });
      return {
        success: false,
        error: 'Input validation failed',
        validationErrors: validationResult.errors,
        data: [],
        errors: [],
        summary: {},
        metadata: {
          processedAt: new Date().toISOString(),
          environment: 'apps_script',
          totalEventsRequested: parseCsvEventIds(csvData).length,
          validEventsProcessed: 0
        }
      };
    }

    const eventIdsToProcess = validationResult.validEventIds;
    enhancedLog('info', '📋 Validated event IDs for processing', { count: eventIdsToProcess.length, eventIds: eventIdsToProcess });

    // Process each event and collect attendance data
    const attendanceData = [];
    const errors = [];

    for (let i = 0; i < eventIdsToProcess.length; i++) {
      const eventId = eventIdsToProcess[i];
      enhancedLog('info', `📅 Processing event ${i + 1}/${eventIdsToProcess.length}`, { eventId });

      try {
        const eventData = processEventAttendance(eventId);
        if (eventData) {
          attendanceData.push(eventData);
          enhancedLog('info', '✅ Event processed successfully', {
            eventId,
            participantCount: eventData.participants?.length || 0
          });
        } else {
          enhancedLog('warn', '⚠️ No data returned for event', { eventId });
        }
      } catch (error) {
        logError('processEventAttendance', error, { eventId, eventIndex: i });
        errors.push({
          eventId: eventId,
          error: error.message
        });
      }

      // Add delay to respect rate limits
      if (i < eventIdsToProcess.length - 1) {
        enhancedLog('info', '⏱️ Rate limiting delay');
        Utilities.sleep(1000); // 1 second delay between requests
      }
    }

    enhancedLog('info', '🎉 Processing complete', {
      attendanceDataCount: attendanceData.length,
      errorsCount: errors.length
    });

    const summary = generateSummary(attendanceData);

    return {
      success: true,
      data: attendanceData,
      errors: errors,
      summary: summary,
      metadata: {
        processedAt: new Date().toISOString(),
        environment: 'apps_script',
        totalEventsRequested: parseCsvEventIds(csvData).length, // Original count before validation
        validEventsProcessed: eventIdsToProcess.length
      }
    };

  } catch (error) {
    logError('processEventIds', error, { csvDataLength: csvData?.length });
    return {
      success: false,
      error: error.message,
      data: [],
      errors: [],
      summary: {},
      metadata: {
        processedAt: new Date().toISOString(),
        environment: 'apps_script'
      }
    };
  }
}



/**
 * Process a single event to get attendance data
 * @param {string} eventId - Google Calendar event ID
 * @return {Object} - Event attendance data
 */
function processEventAttendance(eventId) {
  enhancedLog('info', '📅 Processing attendance for event', { eventId });

  try {
    // Get calendar event details using REST API
    const calendarEvent = getCalendarEventBrowserStyle(eventId);
    if (!calendarEvent) {
      throw new Error('Calendar event not found');
    }

    enhancedLog('info', '✅ Calendar event retrieved', {
      title: calendarEvent.summary,
      start: calendarEvent.start?.dateTime || calendarEvent.start?.date
    });

    // Extract conference data
    const conferenceData = calendarEvent.conferenceData;
    if (!conferenceData || !conferenceData.conferenceId) {
      throw new Error('Event does not have Google Meet conference data');
    }

    // Log the full conference data to understand the structure
    enhancedLog('info', '🔍 Conference data found', {
      conferenceId: conferenceData.conferenceId,
      conferenceData: JSON.stringify(conferenceData, null, 2)
    });

    enhancedLog('info', '🔍 Conference ID found', { conferenceId: conferenceData.conferenceId });

    // Get Meet attendance data using REST API calls
    const meetParticipants = getMeetAttendanceDataBrowserStyle(conferenceData.conferenceId, eventId);

    if (!meetParticipants || meetParticipants.length === 0) {
      throw new Error('No participants found for this meeting');
    }

    enhancedLog('info', '✅ Successfully retrieved Meet attendance data', {
      participantCount: meetParticipants.length
    });

    return {
      eventId: eventId,
      eventTitle: calendarEvent.summary || 'Untitled Event',
      eventStart: calendarEvent.start?.dateTime || calendarEvent.start?.date,
      eventEnd: calendarEvent.end?.dateTime || calendarEvent.end?.date,
      conferenceId: conferenceData.conferenceId,
      meetingUri: conferenceData.entryPoints ? conferenceData.entryPoints[0].uri : null,
      participants: meetParticipants,
      participantCount: meetParticipants.length,
      dataSource: 'meet_api'
    };

  } catch (error) {
    logError('processEventAttendance', error, { eventId });
    throw error;
  }
}

/**
 * Get calendar event details using REST API calls
 * @param {string} eventId - Google Calendar event ID
 * @return {Object} - Calendar event object
 */
function getCalendarEventBrowserStyle(eventId) {
  enhancedLog('info', '📅 Getting calendar event', { eventId });

  try {
    // Get OAuth token for authentication
    const token = ScriptApp.getOAuthToken();

    // Try primary calendar first
    try {
      const url = `https://www.googleapis.com/calendar/v3/calendars/primary/events/${eventId}`;
      const response = UrlFetchApp.fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.getResponseCode() === 200) {
        const calendarEvent = JSON.parse(response.getContentText());
        enhancedLog('info', '✅ Event found in primary calendar');
        return calendarEvent;
      }
    } catch (primaryError) {
      enhancedLog('info', 'ℹ️ Event not found in primary calendar, trying other calendars');
    }

    // If not found in primary, search in all accessible calendars
    try {
      const calendarListUrl = 'https://www.googleapis.com/calendar/v3/users/me/calendarList';
      const calendarListResponse = UrlFetchApp.fetch(calendarListUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (calendarListResponse.getResponseCode() === 200) {
        const calendarList = JSON.parse(calendarListResponse.getContentText());

        for (const calendar of calendarList.items || []) {
          try {
            const eventUrl = `https://www.googleapis.com/calendar/v3/calendars/${encodeURIComponent(calendar.id)}/events/${eventId}`;
            const eventResponse = UrlFetchApp.fetch(eventUrl, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            });

            if (eventResponse.getResponseCode() === 200) {
              const calendarEvent = JSON.parse(eventResponse.getContentText());
              enhancedLog('info', '✅ Event found in calendar', { calendarName: calendar.summary });
              return calendarEvent;
            }
          } catch (e) {
            // Continue searching in other calendars
            continue;
          }
        }
      }
    } catch (listError) {
      enhancedLog('warn', '⚠️ Failed to get calendar list', { error: listError.message });
    }

    throw new Error('Event not found in any accessible calendar');

  } catch (error) {
    logError('getCalendarEventBrowserStyle', error, { eventId });
    throw error;
  }
}

/**
 * Get calendar event details using REST API
 * @param {string} eventId - Google Calendar event ID
 * @return {Object} - Calendar event object
 */
function getCalendarEvent(eventId) {
  // Redirect to REST API implementation
  return getCalendarEventBrowserStyle(eventId);
}

/**
 * Get Meet conference records using REST API
 * @param {string} conferenceId - Google Meet conference ID
 * @return {Array} - Array of conference records
 */
function getMeetConferenceRecords(conferenceId) {
  enhancedLog('info', '🔍 Getting Meet conference records', { conferenceId });

  try {
    const token = ScriptApp.getOAuthToken();
    return findConferenceRecordsBrowserStyle(conferenceId, token);
  } catch (error) {
    logError('getMeetConferenceRecords', error, { conferenceId });
    throw error;
  }
}

/**
 * Get participants for a conference record using REST API
 * @param {string} conferenceRecordName - Conference record name/ID
 * @return {Array} - Array of participant data
 */
function getConferenceParticipants(conferenceRecordName) {
  enhancedLog('info', '👥 Getting participants', { conferenceRecordName });

  try {
    const token = ScriptApp.getOAuthToken();
    return getConferenceParticipantsBrowserStyle(conferenceRecordName, token);
  } catch (error) {
    logError('getConferenceParticipants', error, { conferenceRecordName });
    return [];
  }
}

/**
 * Get participant sessions using REST API
 * @param {string} participantName - Participant name/ID
 * @return {Array} - Array of session data
 */
function getParticipantSessions(participantName) {
  enhancedLog('info', '⏱️ Getting sessions', { participantName });

  try {
    const token = ScriptApp.getOAuthToken();
    return getParticipantSessionsBrowserStyle(participantName, token);
  } catch (error) {
    logError('getParticipantSessions', error, { participantName });
    return [];
  }
}

/**
 * Get Meet attendance data using REST API calls
 * @param {string} conferenceId - Google Meet conference ID from calendar
 * @param {string} eventId - Calendar event ID (optional fallback)
 * @return {Array} - Array of participant attendance data
 */
function getMeetAttendanceDataBrowserStyle(conferenceId, eventId = null) {
  enhancedLog('info', '🔍 Getting Meet attendance data', { conferenceId, eventId });

  try {
    const token = ScriptApp.getOAuthToken();

    // First, try to find conference records
    const conferenceRecords = findConferenceRecordsBrowserStyle(conferenceId, eventId, token);

    if (!conferenceRecords || conferenceRecords.length === 0) {
      throw new Error('No conference records found');
    }

    enhancedLog('info', `📋 Found ${conferenceRecords.length} conference record(s)`);

    // Get participants for each conference record
    const allParticipants = [];
    for (const record of conferenceRecords) {
      const participants = getConferenceParticipantsBrowserStyle(record.name, token);
      allParticipants.push(...participants);
    }

    enhancedLog('info', `✅ Retrieved ${allParticipants.length} participants from Meet API`);
    return allParticipants;

  } catch (error) {
    logError('getMeetAttendanceDataBrowserStyle', error, { conferenceId });
    throw error;
  }
}

/**
 * Find conference records using the working REST API approach
 * @param {string} conferenceId - Conference ID from calendar (meeting code)
 * @param {string} eventId - Calendar event ID (optional)
 * @param {string} token - OAuth token
 * @return {Array} - Array of conference records
 */
function findConferenceRecordsBrowserStyle(conferenceId, eventId, token) {
  enhancedLog('info', '🔍 Searching for conference records using working approach', { conferenceId, eventId });

  try {
    // Use the proven working approach from the reference code
    const baseApiUrl = 'https://meet.googleapis.com/v2/conferenceRecords';

    // Try the meeting code first (this is what works in the reference code)
    const filter = `space.meeting_code="${conferenceId}"`;
    const url = `${baseApiUrl}?filter=${encodeURIComponent(filter)}`;

    enhancedLog('info', '🔍 Trying meeting code search', {
      conferenceId,
      filter,
      url
    });

    const fetchOptions = {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      contentType: 'application/json',
      muteHttpExceptions: true // This is crucial - from working code
    };

    const response = UrlFetchApp.fetch(url, fetchOptions);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    enhancedLog('info', '📡 API Response', {
      status: responseCode,
      contentLength: responseBody.length
    });

    if (responseCode === 200) {
      const jsonResponse = JSON.parse(responseBody);

      if (jsonResponse.conferenceRecords && jsonResponse.conferenceRecords.length > 0) {
        enhancedLog('info', `✅ Found ${jsonResponse.conferenceRecords.length} conference record(s)`);

        // Log details of found records
        jsonResponse.conferenceRecords.forEach((record, index) => {
          enhancedLog('info', `📋 Conference record ${index + 1}`, {
            name: record.name,
            spaceName: record.space?.name,
            meetingCode: record.space?.meetingCode,
            startTime: record.startTime,
            endTime: record.endTime
          });
        });

        return jsonResponse.conferenceRecords;
      } else {
        enhancedLog('info', 'ℹ️ No conference records found in response');
        return [];
      }
    } else {
      enhancedLog('error', '❌ API request failed', {
        status: responseCode,
        error: responseBody
      });

      // Try to parse error details if available
      try {
        const errorDetails = JSON.parse(responseBody);
        enhancedLog('error', '📋 Parsed error details', errorDetails);
      } catch (e) {
        enhancedLog('error', '📋 Raw error response', { responseBody });
      }

      throw new Error(`Meet API request failed: ${responseCode} - ${responseBody}`);
    }

  } catch (error) {
    logError('findConferenceRecordsBrowserStyle', error, { conferenceId, eventId });
    throw error;
  }
}



/**
 * Get participants for a conference record using the working REST API approach
 * @param {string} conferenceRecordName - Conference record name/ID
 * @param {string} token - OAuth token
 * @return {Array} - Array of participant data
 */
function getConferenceParticipantsBrowserStyle(conferenceRecordName, token) {
  enhancedLog('info', '👥 Getting participants using working approach', { conferenceRecordName });

  try {
    const allParticipants = [];
    let pageToken = null;
    const MAX_PAGES_TO_FETCH = 10; // Safety break
    let pagesFetched = 0;

    const baseApiUrl = `https://meet.googleapis.com/v2/${conferenceRecordName}/participants`;

    do {
      let url = baseApiUrl;
      const queryParams = [];
      queryParams.push('pageSize=250'); // Max page size from working code

      if (pageToken) {
        queryParams.push(`pageToken=${pageToken}`);
      }

      if (queryParams.length > 0) {
        url += '?' + queryParams.join('&');
      }

      enhancedLog('info', '🔍 Fetching participants', { url });

      const fetchOptions = {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        contentType: 'application/json',
        muteHttpExceptions: true // Crucial from working code
      };

      const response = UrlFetchApp.fetch(url, fetchOptions);
      const responseCode = response.getResponseCode();
      const responseBody = response.getContentText();

      enhancedLog('info', '📡 Participants API Response', {
        status: responseCode,
        contentLength: responseBody.length
      });

      if (responseCode === 200) {
        const jsonResponse = JSON.parse(responseBody);

        if (jsonResponse.participants && jsonResponse.participants.length > 0) {
          enhancedLog('info', `✅ Fetched ${jsonResponse.participants.length} participants from this page`);

          // Process participants
          for (const participant of jsonResponse.participants) {
            // Get detailed session information for each participant
            const sessions = getParticipantSessionsBrowserStyle(participant.name, token);

            let email = null;
            let name = 'Anonymous User';

            if (participant.signedinUser) {
              name = participant.signedinUser.displayName || 'Signed-in User';

              // Check what email information is directly available in Meet API
              if (participant.signedinUser.user && participant.signedinUser.user.includes('@')) {
                email = participant.signedinUser.user;
                enhancedLog('info', '✅ Found email directly in user field', { email });
              } else if (participant.signedinUser.email) {
                email = participant.signedinUser.email;
                enhancedLog('info', '✅ Found email in dedicated email field', { email });
              } else if (participant.signedinUser.user) {
                // Try to get email using People API
                enhancedLog('info', '🔍 Attempting People API lookup', {
                  user: participant.signedinUser.user,
                  participantName: name
                });
                email = getEmailFromPeopleAPI(participant.signedinUser.user, token);

                if (email) {
                  enhancedLog('info', '✅ Successfully retrieved email via People API', { email });
                } else {
                  enhancedLog('info', 'ℹ️ No email found via People API', {
                    participantName: name,
                    user: participant.signedinUser.user
                  });
                }
              } else {
                enhancedLog('info', 'ℹ️ No email available for signed-in user', {
                  participantName: name
                });
              }
            } else if (participant.anonymousUser) {
              name = participant.anonymousUser.displayName || 'Anonymous User';
              enhancedLog('info', 'ℹ️ Anonymous user - no email available');
            } else if (participant.phoneUser) {
              name = `Phone User (${participant.phoneUser.displayName || 'Unknown'})`;
              enhancedLog('info', 'ℹ️ Phone user - no email available');
            }

            allParticipants.push({
              name: name,
              email: email,
              participantId: participant.name,
              sessions: sessions,
              totalDuration: calculateTotalDuration(sessions)
            });
          }
        } else {
          enhancedLog('info', 'ℹ️ No participants found on this page');
        }

        pageToken = jsonResponse.nextPageToken;
      } else {
        enhancedLog('error', '❌ Participants API request failed', {
          status: responseCode,
          error: responseBody
        });

        try {
          const errorDetails = JSON.parse(responseBody);
          enhancedLog('error', '📋 Parsed error details', errorDetails);
        } catch (e) {
          enhancedLog('error', '📋 Raw error response', { responseBody });
        }

        throw new Error(`Participants API request failed: ${responseCode} - ${responseBody}`);
      }

      pagesFetched++;
    } while (pageToken && pagesFetched < MAX_PAGES_TO_FETCH);

    if (pageToken && pagesFetched >= MAX_PAGES_TO_FETCH) {
      enhancedLog('warn', `⚠️ Reached maximum pages to fetch (${MAX_PAGES_TO_FETCH}). There might be more participants.`);
    }

    enhancedLog('info', `✅ Total participants retrieved: ${allParticipants.length}`);
    return allParticipants;

  } catch (error) {
    logError('getConferenceParticipantsBrowserStyle', error, { conferenceRecordName });
    return [];
  }
}

/**
 * Get email address from People API using resource name (Apps Script version, uses UrlFetchApp)
 * @param {string} resourceName - People API resource name or user ID
 * @param {string} token - OAuth token
 * @return {string|null} - Email address or null if not found
 */
function getEmailFromPeopleAPI(resourceName, token) {
  enhancedLog('info', '📧 Getting email from People API (UrlFetchApp)', { resourceName });

  try {
    let formattedResourceName = resourceName;
    if (resourceName.startsWith('users/')) {
      formattedResourceName = resourceName.replace('users/', 'people/');
      enhancedLog('info', '🔄 Converted users/ to people/ format', { formattedResourceName });
    } else if (!resourceName.startsWith('people/')) {
      formattedResourceName = `people/${resourceName}`;
      enhancedLog('info', '🔄 Added people/ prefix', { formattedResourceName });
    }

    const apiUrl = `https://people.googleapis.com/v1/${formattedResourceName}?personFields=emailAddresses,names`;
    enhancedLog('info', '🔍 Making People API request via UrlFetchApp', { apiUrl });

    const fetchOptions = {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      muteHttpExceptions: true // Handle errors manually
    };

    const response = UrlFetchApp.fetch(apiUrl, fetchOptions);
    const responseCode = response.getResponseCode();
    const responseBody = response.getContentText();

    enhancedLog('info', '📡 People API Response (UrlFetchApp)', {
      status: responseCode,
      contentLength: responseBody.length
    });

    if (responseCode === 200) {
      const person = JSON.parse(responseBody);
      enhancedLog('info', '✅ People API response received (UrlFetchApp)', {
        hasEmailAddresses: !!(person.emailAddresses && person.emailAddresses.length > 0),
        emailCount: person.emailAddresses ? person.emailAddresses.length : 0
      });

      if (person.emailAddresses && person.emailAddresses.length > 0) {
        const primaryEmail = person.emailAddresses.find(email => email.metadata?.primary) || person.emailAddresses[0];
        enhancedLog('info', '✅ Found email via People API (UrlFetchApp)', { email: primaryEmail.value });
        return primaryEmail.value;
      } else {
        enhancedLog('info', 'ℹ️ No email addresses found in People API response (UrlFetchApp)');
        return null;
      }
    } else {
      enhancedLog('warn', '⚠️ People API request failed (UrlFetchApp)', {
        status: responseCode,
        error: responseBody,
        resourceName: formattedResourceName
      });

      // Log specific error details
      try {
        const errorDetails = JSON.parse(responseBody);
        enhancedLog('error', '📋 Parsed error details (People API)', errorDetails);
        if (responseCode === 403) {
             enhancedLog('warn', '⚠️ People API access forbidden (403) - check scopes, API enablement, and if user is a contact.');
        } else if (responseCode === 404) {
             enhancedLog('warn', '⚠️ User not found in People API (404) - may be external or not a contact.');
        }
      } catch (e) {
        // If responseBody is not JSON
        enhancedLog('error', '📋 Raw error response (People API)', { responseBody });
      }
      return null;
    }
  } catch (error) {
    // Catch-all for unexpected errors during the process
    logError('getEmailFromPeopleAPI (UrlFetchApp)', error, { resourceName });
    return null;
  }
}

/**
 * Get participant sessions using the working REST API approach
 * @param {string} participantName - Participant name/ID
 * @param {string} token - OAuth token
 * @return {Array} - Array of session data
 */
function getParticipantSessionsBrowserStyle(participantName, token) {
  enhancedLog('info', '⏱️ Getting sessions using working approach', { participantName });

  try {
    const allSessions = [];
    let pageToken = null;
    const MAX_PAGES_TO_FETCH = 10; // Safety break
    let pagesFetched = 0;

    const baseApiUrl = `https://meet.googleapis.com/v2/${participantName}/participantSessions`;

    do {
      let url = baseApiUrl;
      const queryParams = [];
      queryParams.push('pageSize=100');

      if (pageToken) {
        queryParams.push(`pageToken=${pageToken}`);
      }

      if (queryParams.length > 0) {
        url += '?' + queryParams.join('&');
      }

      const fetchOptions = {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        contentType: 'application/json',
        muteHttpExceptions: true // Crucial from working code
      };

      const response = UrlFetchApp.fetch(url, fetchOptions);
      const responseCode = response.getResponseCode();
      const responseBody = response.getContentText();

      if (responseCode === 200) {
        const jsonResponse = JSON.parse(responseBody);

        if (jsonResponse.participantSessions && jsonResponse.participantSessions.length > 0) {
          const sessions = jsonResponse.participantSessions.map(session => ({
            startTime: session.startTime,
            endTime: session.endTime,
            duration: calculateSessionDuration(session.startTime, session.endTime)
          }));

          allSessions.push(...sessions);
        }

        pageToken = jsonResponse.nextPageToken;
      } else {
        enhancedLog('warn', '⚠️ Sessions API request failed', {
          status: responseCode,
          error: responseBody,
          participantName
        });

        // Don't throw error for sessions - just return empty array
        return [];
      }

      pagesFetched++;
    } while (pageToken && pagesFetched < MAX_PAGES_TO_FETCH);

    return allSessions;

  } catch (error) {
    logError('getParticipantSessionsBrowserStyle', error, { participantName });
    return [];
  }
}



// ============================================================================
// APPS SCRIPT SPECIFIC FUNCTIONS
// ============================================================================

/**
 * Test function to validate the setup
 * @return {Object} - Test results
 */
function testSetup() {
  enhancedLog('info', '🧪 Testing Apps Script setup');

  const results = {
    calendarAccess: false,
    meetAccess: false,
    urlFetchAccess: false,
    errors: [],
    environment: 'apps_script',
    timestamp: new Date().toISOString()
  };

  // Test Calendar API access using REST API
  try {
    const token = ScriptApp.getOAuthToken();
    const url = 'https://www.googleapis.com/calendar/v3/calendars/primary/events?maxResults=1';
    const response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.getResponseCode() === 200) {
      results.calendarAccess = true;
      enhancedLog('info', '✅ Calendar API access: OK');
    } else {
      results.errors.push(`Calendar API: HTTP ${response.getResponseCode()}`);
    }
  } catch (error) {
    results.errors.push('Calendar API: ' + error.message);
    logError('testSetup - Calendar API', error);
  }

  // Test Meet API access using REST API
  try {
    const token = ScriptApp.getOAuthToken();
    const url = 'https://meet.googleapis.com/v2/conferenceRecords?pageSize=1';
    const response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.getResponseCode() === 200) {
      results.meetAccess = true;
      enhancedLog('info', '✅ Meet API access: OK');
    } else {
      results.errors.push(`Meet API: HTTP ${response.getResponseCode()}`);
    }
  } catch (error) {
    results.errors.push('Meet API: ' + error.message);
    logError('testSetup - Meet API', error);
  }

  // Test UrlFetchApp access (Apps Script specific)
  try {
    const token = ScriptApp.getOAuthToken();
    if (token) {
      results.urlFetchAccess = true;
      enhancedLog('info', '✅ UrlFetchApp and OAuth token access: OK');
    } else {
      results.errors.push('UrlFetchApp: No OAuth token available');
    }
  } catch (error) {
    results.errors.push('UrlFetchApp: ' + error.message);
    logError('testSetup - UrlFetchApp', error);
  }

  enhancedLog('info', '🧪 Setup test complete', {
    calendarAccess: results.calendarAccess,
    meetAccess: results.meetAccess,
    urlFetchAccess: results.urlFetchAccess,
    errorCount: results.errors.length
  });

  return results;
}

/**
 * Get sample event for testing
 * @return {Object} - Sample event data
 */
function getSampleEvent() {
  enhancedLog('info', '🔍 Getting sample event for testing');

  try {
    // Get the most recent events from primary calendar using REST API
    const token = ScriptApp.getOAuthToken();
    const timeMin = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(); // Last 30 days
    const url = `https://www.googleapis.com/calendar/v3/calendars/primary/events?maxResults=10&orderBy=startTime&singleEvents=true&timeMin=${encodeURIComponent(timeMin)}`;

    const response = UrlFetchApp.fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.getResponseCode() !== 200) {
      throw new Error(`Calendar API request failed: ${response.getResponseCode()} ${response.getContentText()}`);
    }

    const events = JSON.parse(response.getContentText());

    enhancedLog('info', '📅 Retrieved calendar events', {
      eventCount: events.items?.length || 0
    });

    if (events.items && events.items.length > 0) {
      // Find an event with conference data
      for (const event of events.items) {
        if (event.conferenceData && event.conferenceData.conferenceId) {
          enhancedLog('info', '✅ Found event with conference data', {
            eventTitle: event.summary,
            conferenceId: event.conferenceData.conferenceId
          });

          return {
            eventId: event.id,
            title: event.summary,
            conferenceId: event.conferenceData.conferenceId,
            start: event.start.dateTime || event.start.date,
            environment: 'apps_script'
          };
        }
      }
    }

    const errorMsg = 'No events with conference data found in the last 30 days';
    enhancedLog('warn', '⚠️ ' + errorMsg);
    return { error: errorMsg };

  } catch (error) {
    logError('getSampleEvent', error);
    return { error: error.message };
  }
}

/**
 * Test function to get participants for a specific meeting ID
 * @return {Object} - Test results with participant data
 */
function testGetMeetingParticipants() {
  const meetingId = 'ntrm362tko57fr66flv9r1qm79_20250528T173000Z';
  enhancedLog('info', '🧪 Testing participant retrieval for specific meeting', { meetingId });

  const results = {
    meetingId: meetingId,
    success: false,
    participants: [],
    conferenceRecords: [],
    errors: [],
    timestamp: new Date().toISOString()
  };

  try {
    // Step 1: Try to find conference records for this meeting
    enhancedLog('info', '🔍 Step 1: Finding conference records');

    try {
      const token = ScriptApp.getOAuthToken();
      const conferenceRecords = findConferenceRecordsBrowserStyle(meetingId, meetingId, token);

      results.conferenceRecords = conferenceRecords;
      enhancedLog('info', `✅ Found ${conferenceRecords.length} conference records`);

      // Step 2: Get participants for each conference record
      if (conferenceRecords.length > 0) {
        enhancedLog('info', '🔍 Step 2: Getting participants');

        const allParticipants = [];
        for (const record of conferenceRecords) {
          enhancedLog('info', `📋 Processing conference record: ${record.name}`);

          try {
            const participants = getConferenceParticipantsBrowserStyle(record.name, token);
            allParticipants.push(...participants);

            enhancedLog('info', `✅ Retrieved ${participants.length} participants from record ${record.name}`);
          } catch (participantError) {
            const errorMsg = `Failed to get participants for record ${record.name}: ${participantError.message}`;
            results.errors.push(errorMsg);
            enhancedLog('error', errorMsg);
          }
        }

        results.participants = allParticipants;
        results.success = allParticipants.length > 0;

        enhancedLog('info', `🎉 Test completed successfully`, {
          totalParticipants: allParticipants.length,
          conferenceRecords: conferenceRecords.length
        });

      } else {
        const errorMsg = 'No conference records found for this meeting ID';
        results.errors.push(errorMsg);
        enhancedLog('warn', '⚠️ ' + errorMsg);
      }

    } catch (conferenceError) {
      const errorMsg = `Failed to find conference records: ${conferenceError.message}`;
      results.errors.push(errorMsg);
      logError('testGetMeetingParticipants - conference records', conferenceError, { meetingId });
    }

  } catch (error) {
    const errorMsg = `Test failed: ${error.message}`;
    results.errors.push(errorMsg);
    logError('testGetMeetingParticipants', error, { meetingId });
  }

  // Log final results
  enhancedLog('info', '📊 OAuth Debug Summary', {
    tokenPresent: !!results.oauthToken,
    meetTestsCount: results.meetApiTests.length,
    meetSuccessCount: results.meetApiTests.filter(t => t.success).length,
    calendarTestsCount: results.calendarApiTests.length,
    calendarSuccessCount: results.calendarApiTests.filter(t => t.success).length,
    errorCount: results.errors.length
  });

  return results;
}

/**
 * Test People API access and email retrieval
 * @return {Object} - Test results
 */
function testPeopleAPI() {
  enhancedLog('info', '🧪 Testing People API access');

  const results = {
    peopleApiAccess: false,
    currentUserEmail: null,
    errors: [],
    timestamp: new Date().toISOString()
  };

  try {
    const token = ScriptApp.getOAuthToken();

    // Test getting current user's email
    try {
      const url = 'https://people.googleapis.com/v1/people/me?personFields=emailAddresses,names';
      const response = UrlFetchApp.fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        muteHttpExceptions: true
      });

      if (response.getResponseCode() === 200) {
        const data = JSON.parse(response.getContentText());
        results.peopleApiAccess = true;

        if (data.emailAddresses && data.emailAddresses.length > 0) {
          const primaryEmail = data.emailAddresses.find(email => email.metadata?.primary) ||
                              data.emailAddresses[0];
          results.currentUserEmail = primaryEmail.value;
        }

        enhancedLog('info', '✅ People API access: OK', {
          email: results.currentUserEmail
        });
      } else {
        results.errors.push(`People API: HTTP ${response.getResponseCode()} - ${response.getContentText()}`);
        enhancedLog('error', '❌ People API access failed', {
          status: response.getResponseCode(),
          error: response.getContentText()
        });
      }
    } catch (error) {
      results.errors.push('People API: ' + error.message);
      logError('testPeopleAPI', error);
    }

  } catch (error) {
    results.errors.push('OAuth token: ' + error.message);
    logError('testPeopleAPI - OAuth', error);
  }

  enhancedLog('info', '🧪 People API test complete', {
    peopleApiAccess: results.peopleApiAccess,
    currentUserEmail: results.currentUserEmail,
    errorCount: results.errors.length
  });

  return results;
}

/**
 * Get application version and environment info
 * @return {Object} - Version and environment information
 */
function getAppInfo() {
  return {
    version: '2.3.0',
    environment: 'apps_script',
    timestamp: new Date().toISOString(),
    features: {
      meetApiIntegration: true,
      calendarApiIntegration: true,
      urlFetchSupport: true,
      enhancedLogging: true,
      browserStyleApisOnly: true,
      realDataOnly: true,
      peopleApiIntegration: true
    }
  };
}
