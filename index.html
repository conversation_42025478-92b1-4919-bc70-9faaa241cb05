<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Meet Attendance Export</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        <?!= include('style'); ?>
    </style>
</head>
<body class="font-sans text-gray-800 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen py-6">
    <!-- Development Environment Banner -->
    <? if (environmentInfo && environmentInfo.isDev) { ?>
    <div id="dev-banner" class="bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 px-4 text-center font-bold shadow-lg border-b-4 border-red-600 relative">
        <div class="max-w-5xl mx-auto flex items-center justify-center space-x-3">
            <span class="text-xl">🚧</span>
            <span class="text-sm md:text-base">DEVELOPMENT ENVIRONMENT</span>
            <span class="text-xl">🚧</span>
        </div>
        <div class="text-xs mt-1 opacity-90">
            This is a development deployment - Data may be test data
        </div>
        <button id="dev-banner-close" class="absolute top-2 right-2 text-white hover:text-gray-200 text-xl font-bold leading-none" onclick="document.getElementById('dev-banner').style.display='none'" title="Hide banner">
            ×
        </button>
    </div>
    <? } ?>

    <div class="max-w-5xl mx-auto p-3 md:p-6">
        <header class="text-center mb-8 p-5 bg-white rounded-xl shadow-lg transform transition-all duration-300 hover:scale-102 hover:shadow-xl">
            <h1 class="text-blue-800 mb-2 text-3xl md:text-4xl font-extrabold tracking-tight drop-shadow-sm">Google Meet Attendance Export</h1>
            <p class="text-gray-700 text-base md:text-lg font-light italic leading-relaxed">Effortlessly export attendance data for multiple Google Meet sessions using Calendar event IDs</p>
        </header>

        <main>
            <section class="bg-white mb-6 p-6 rounded-xl shadow-lg">
                <h2 class="text-blue-800 mb-5 border-b-2 border-blue-200 pb-3 text-2xl font-bold">Input Event IDs</h2>
                <form id="eventForm">
                    <div class="mb-5">
                        <label for="csvInput" class="block mb-2 font-semibold text-gray-800 text-base">
                            Enter Google Calendar Event IDs (CSV format):
                            <span class="block text-xs text-gray-600 mt-1">One event ID per line, or comma-separated.</span>
                        </label>

                        <details class="mb-3 text-sm text-gray-700 cursor-pointer">
                            <summary class="font-medium hover:text-blue-700 transition-colors">How to find a Google Calendar Event ID</summary>
                            <div class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <ol class="list-decimal list-inside space-y-1">
                                    <li>Open Google Calendar (<a href="https://calendar.google.com" target="_blank" class="text-blue-600 hover:underline">calendar.google.com</a>).</li>
                                    <li>Find and click on the event you want to export.</li>
                                    <li>In the event details pop-up, click the three vertical dots (Options).</li>
                                    <li>Select "Troubleshooting info".</li>
                                    <li>Copy the string after "Event ID: ". It usually looks something like <code><EMAIL></code>.</li>
                                </ol>
                            </div>
                        </details>

                        <textarea
                            id="csvInput"
                            name="csvInput"
                            rows="8"
                            placeholder="e.g., event_id, another_event_id_here"
                            required
                            class="w-full p-3 border-2 border-blue-400 rounded-lg font-mono text-sm resize-y focus:outline-none focus:ring-2 focus:ring-blue-300 transition-all duration-300 shadow-sm"
                        ></textarea>
                        <div id="csvInputError" class="text-red-600 text-xs mt-1 h-4"></div> <!-- Placeholder for validation message -->
                    </div>

                    <div class="flex gap-3 items-center flex-wrap">
                        <button type="button" id="submitBtn" class="btn bg-blue-700 hover:bg-blue-800 text-white font-bold py-2.5 px-6 rounded-lg text-base inline-flex items-center justify-center gap-2 transition-all duration-300 transform hover:-translate-y-0.5 shadow-md hover:shadow-lg disabled:opacity-60 disabled:cursor-not-allowed">
                            <span class="btn-text">Process Events</span>
                            <span class="loading-spinner w-5 h-5 border-2 border-transparent border-t-2 border-white rounded-full animate-spin" style="display: none;"></span>
                        </button>
                        <button type="button" id="clearBtn" class="btn bg-gray-200 text-gray-800 border border-gray-300 font-semibold py-2.5 px-6 rounded-lg text-base inline-flex items-center justify-center gap-2 transition-all duration-300 hover:bg-gray-300 transform hover:-translate-y-0.5 shadow-md hover:shadow-lg disabled:opacity-60 disabled:cursor-not-allowed">Clear</button>
                    </div>
                </form>
            </section>

            <section class="bg-white mb-6 p-6 rounded-xl shadow-lg" id="progressSection" style="display: none;">
                <h3 class="text-blue-800 mb-5 border-b-2 border-blue-200 pb-3 text-xl font-bold">Processing Progress</h3>
                <div class="w-full h-6 bg-gray-200 rounded-full overflow-hidden mb-3 shadow-inner">
                    <div class="h-full bg-gradient-to-r from-blue-500 to-blue-700 transition-all duration-300 ease-in-out" id="progressFill" style="width: 0%;"></div>
                </div>
                <p id="progressText" class="text-gray-700 text-sm text-center">Initializing...</p>
            </section>

            <section class="bg-white mb-6 p-6 rounded-xl shadow-lg" id="errorSection" style="display: none;">
                <h3 class="text-blue-800 mb-5 border-b-2 border-blue-200 pb-3 text-xl font-bold">Errors</h3>
                <div id="errorList" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"></div>
            </section>

            <section class="bg-white mb-6 p-6 rounded-xl shadow-lg" id="summarySection" style="display: none;">
                <h3 class="text-blue-800 mb-5 border-b-2 border-blue-200 pb-3 text-xl font-bold">Summary Statistics</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="summaryGrid"></div>
            </section>

            <section class="bg-white mb-6 p-6 rounded-xl shadow-lg" id="resultsSection" style="display: none;">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-5 gap-4">
                    <h3 class="text-blue-800 text-xl font-bold">Attendance Results</h3>
                    <div class="flex gap-3 items-center w-full md:w-auto">
                        <input type="text" id="searchInput" placeholder="Search participants..." class="search-input flex-grow p-2.5 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm">
                        <button id="exportBtn" class="btn bg-gray-200 text-gray-800 border border-gray-300 font-semibold py-2.5 px-6 rounded-lg text-sm inline-flex items-center justify-center gap-2 transition-all duration-300 hover:bg-gray-300 transform hover:-translate-y-0.5 shadow-md hover:shadow-lg disabled:opacity-60 disabled:cursor-not-allowed">Export CSV</button>
                    </div>
                </div>

                <div class="overflow-x-auto border border-gray-300 rounded-lg shadow-md">
                    <table id="resultsTable" class="min-w-full divide-y divide-gray-200 bg-white">
                        <thead class="bg-gray-100">
                            <tr>
                                <th data-sort="eventTitle" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer select-none relative hover:bg-gray-200 transition-colors duration-200">Event Title</th>
                                <th data-sort="eventStart" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer select-none relative hover:bg-gray-200 transition-colors duration-200">Date/Time</th>
                                <th data-sort="participantName" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer select-none relative hover:bg-gray-200 transition-colors duration-200">Participant Name</th>
                                <th data-sort="participantEmail" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer select-none relative hover:bg-gray-200 transition-colors duration-200">Email</th>
                                <th data-sort="totalDuration" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer select-none relative hover:bg-gray-200 transition-colors duration-200">Duration (min)</th>
                                <th data-sort="sessionCount" class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider cursor-pointer select-none relative hover:bg-gray-200 transition-colors duration-200">Sessions</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-700 uppercase tracking-wider">Join/Leave Times</th>
                            </tr>
                        </thead>
                        <tbody id="resultsTableBody" class="bg-white divide-y divide-gray-100">
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>

    <!-- Main script -->
    <script>
        <?!= include('script'); ?>
    </script>
</body>
</html>
