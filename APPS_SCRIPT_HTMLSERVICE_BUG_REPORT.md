# Bug Report: Google Apps Script HtmlService Fails to Process Included JavaScript with Unescaped `<` and `>`

**Date:** 2025-05-28
**Project:** Google Meet Attendance Export (Apps Script Web App)

## 1. Issue Description

When using `HtmlService.createHtmlOutputFromFile(filename).getContent()` to include JavaScript content within a `<script>` tag in an Apps Script web app (via `<?!= include('script_file'); ?>`), the service fails to correctly process the JavaScript file if it contains unescaped less-than (`<`) or greater-than (`>`) characters, even when these characters are part of valid JavaScript syntax (e.g., in comparison operations like `if (a < b)`).

This failure is silent on the server-side (no errors logged in Apps Script dashboard) and results in the client-side script either not being included at all or being included in a malformed way, preventing any of its JavaScript from executing. This often manifests as a blank page or a page with non-functional UI elements, with no errors in the browser console indicating a script loading failure.

## 2. Steps to Reproduce

**Setup:**
1.  Create a new Google Apps Script project.
2.  Create an `index.html` file:
    ```html
    <!DOCTYPE html>
    <html>
      <head>
        <base target="_top">
      </head>
      <body>
        <h1>Test Page</h1>
        <button id="testButton">Test Button</button>
        <?!= include('problematic_script'); ?>
        <script>
          console.log("Inline script in index.html executed.");
        </script>
      </body>
    </html>
    ```
3.  Create a `Code.gs` (or `Code.js`) file:
    ```javascript
    function doGet() {
      return HtmlService.createTemplateFromFile('index').evaluate();
    }

    function include(filename) {
      return HtmlService.createHtmlOutputFromFile(filename).getContent();
    }
    ```
4.  Create a `problematic_script.html` file with the following content:
    ```html
    <script>
    console.log("Problematic_script.html: Script loading..."); // Log 1
    alert("Problematic_script.html: Script loaded!");     // Alert 1

    function testSortProblem() {
        console.log("Problematic_script.html: testSortProblem called");
        const arr = [3, 1, 2];
        arr.sort((a, b) => {
            // The following line causes the issue:
            if (a < b) return -1; // Using '<'
            if (a > b) return 1;  // Using '>'
            return 0;
        });
        console.log("Problematic_script.html: Array sorted (or attempted).");
        alert("Problematic_script.html: Sort function executed.");
    }

    document.addEventListener('DOMContentLoaded', () => {
        console.log("Problematic_script.html: DOMContentLoaded.");
        const btn = document.getElementById('testButton');
        if (btn) {
            btn.addEventListener('click', testSortProblem);
            console.log("Problematic_script.html: Button listener added.");
        } else {
            console.error("Problematic_script.html: Test button not found.");
        }
    });
    </script>
    ```
5.  Deploy the project as a web app (e.g., "Execute as: Me", "Who has access: Anyone"). Use the `/dev` URL for testing.

**Expected Behavior:**
*   The page loads, showing "Test Page" and "Test Button".
*   An alert "Problematic_script.html: Script loaded!" appears.
*   The browser console shows:
    *   "Problematic_script.html: Script loading..."
    *   "Problematic_script.html: DOMContentLoaded."
    *   "Problematic_script.html: Button listener added."
    *   "Inline script in index.html executed."
*   Clicking "Test Button" triggers the `testSortProblem` function, showing an alert "Problematic_script.html: Sort function executed."

**Actual Behavior:**
*   The page loads, showing "Test Page" and "Test Button".
*   **No alert** "Problematic_script.html: Script loaded!" appears.
*   The browser console **only shows** "Inline script in index.html executed." None of the console logs from `problematic_script.html` appear.
*   Clicking "Test Button" does nothing, as the event listener from `problematic_script.html` was never attached.
*   This indicates that the content from `problematic_script.html` was not correctly processed and included by `HtmlService`.

## 3. Further Investigation & Workaround

*   If the lines `if (a < b) return -1;` and `if (a > b) return 1;` in `problematic_script.html` are commented out, or if `problematic_script.html` is replaced with a script that does not contain these specific `<` or `>` characters in JS logic, the script loads and executes as expected.
*   Attempting to use HTML entities (`<`, `>`) in place of `<` and `>` also results in the script failing to load/execute correctly, suggesting these entities are not decoded back to their character equivalents before JavaScript parsing in this context, or are themselves problematic for `HtmlService`.
*   **Workaround:** The issue can be resolved by rewriting the JavaScript logic to avoid these characters. For example, the problematic sort comparison can be changed to:
    ```javascript
    // arr.sort((a, b) => {
    //     if (a < b) return -1;
    //     if (a > b) return 1;
    //     return 0;
    // });
    // Rewritten as:
    arr.sort((a, b) => {
        let diff = a - b; // Assumes numbers for simplicity of example
        if (diff === 0) return 0;
        return Math.sign(diff); // Returns -1, 0, or 1
    });
    ```
    Or for mixed types, as identified in the original debugging session:
    ```javascript
    arr.sort((a,b) => {
        let aVal = a; // Simplified for example
        let bVal = b; // Simplified for example
        let diff = 0;
        if (typeof aVal === 'string' && typeof bVal === 'string') {
            diff = aVal.localeCompare(bVal);
        } else if (aVal instanceof Date && bVal instanceof Date) {
            diff = aVal.getTime() - bVal.getTime();
        } else { // Assume numbers
            diff = aVal - bVal;
        }
        let resultSign = 0;
        if (diff !== 0) resultSign = Math.sign(diff);
        if (isNaN(resultSign)) resultSign = 0;
        // Assuming sortDirection = 'asc' for simplicity here
        return resultSign;
    });
    ```
    With this workaround, `HtmlService` processes the included script correctly.

## 4. Environment

*   Google Apps Script Web App environment.
*   Issue observed using the `/dev` URL for the web app.
*   Browsers tested: (User should fill this in, e.g., Google Chrome Version X, Firefox Version Y)

## 5. Impact

This bug can make it very difficult to debug client-side JavaScript in Apps Script web apps, as the failure is silent and gives no indication that the script content itself is the cause of the `HtmlService` processing failure. Developers may spend significant time looking for runtime errors when the script is not even being loaded correctly. It forces developers to avoid common and valid JavaScript syntax (direct `<` and `>` comparisons) in included files.
