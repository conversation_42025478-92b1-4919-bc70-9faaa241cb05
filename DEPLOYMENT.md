# Deployment Guide

This guide walks you through deploying the Google Meet Attendance Export application as a Google Apps Script web app.

## Prerequisites

1. Google account with access to Google Apps Script
2. Google Workspace account with Google Meet (for accessing Meet API)
3. Administrative permissions to enable APIs (if required by your organization)

## Step-by-Step Deployment

### 1. Create New Apps Script Project

1. Go to [script.google.com](https://script.google.com)
2. Click "New project"
3. Give your project a meaningful name (e.g., "Meet Attendance Export")

### 2. Upload Project Files

Replace the default `Code.gs` file and add the other files:

1. **Code.js**: Copy the entire content from `Code.js`
2. **index.html**: Create new HTML file and copy content from `index.html`
3. **style.css**: Create new HTML file and copy content from `style.css`
4. **script.js**: Create new HTML file and copy content from `script.js`
5. **appsscript.json**: Replace the manifest file with content from `appsscript.json`

### 3. Enable Advanced Google Services

1. In the Apps Script editor, click on "Services" in the left sidebar
2. Click "Add a service"
3. Find and add:
   - **Google Calendar API** (version v3)
   - **Google Meet API** (version v1)

**Note**: If Google Meet API is not available, you may need to:
- Contact your Google Workspace administrator
- Ensure your organization has the appropriate Google Workspace plan
- Check if Meet API is enabled for your domain

### 4. Configure OAuth Scopes

The `appsscript.json` file should automatically configure the required scopes:
- `https://www.googleapis.com/auth/calendar.readonly`
- `https://www.googleapis.com/auth/meetings.space.readonly`
- `https://www.googleapis.com/auth/script.webapp.deploy`

If you need to manually add scopes:
1. Go to "Project Settings" (gear icon)
2. Scroll to "OAuth Scopes"
3. Add the scopes listed above

### 5. Test the Setup

Before deploying, test the API access:

1. In the Apps Script editor, select the `testSetup` function
2. Click "Run"
3. Authorize the application when prompted
4. Check the execution log for any errors

### 6. Deploy as Web App

1. Click "Deploy" > "New deployment"
2. Click the gear icon next to "Type" and select "Web app"
3. Configure the deployment:
   - **Description**: "Meet Attendance Export v1.0"
   - **Execute as**: "Me (<EMAIL>)"
   - **Who has access**: Choose based on your needs:
     - "Only myself" - Only you can access
     - "Anyone within [your domain]" - Anyone in your organization
     - "Anyone" - Public access (not recommended for sensitive data)
4. Click "Deploy"
5. Copy the web app URL provided

### 7. Test the Deployment

1. Open the web app URL in a new browser tab
2. You should see the "Google Meet Attendance Export" interface
3. Test with a sample event ID (use `getSampleEvent()` function to find one)

## Troubleshooting Deployment Issues

### Common Problems

#### 1. "Google Meet API not found"
- **Cause**: Meet API may not be available for your Google Workspace plan
- **Solution**: 
  - Contact your Google Workspace administrator
  - Consider using alternative methods to access Meet data
  - Check if your organization has the required Google Workspace plan

#### 2. "Authorization required"
- **Cause**: OAuth scopes not properly configured
- **Solution**:
  - Re-run the `testSetup()` function
  - Manually add required scopes in Project Settings
  - Clear authorization and re-authorize

#### 3. "Calendar API access denied"
- **Cause**: Insufficient permissions or API not enabled
- **Solution**:
  - Ensure Google Calendar API is enabled
  - Check that you have access to the calendars you're trying to read
  - Verify OAuth scopes include calendar.readonly

#### 4. "Web app not accessible"
- **Cause**: Deployment configuration issues
- **Solution**:
  - Check "Who has access" settings
  - Ensure "Execute as" is set to "Me"
  - Try creating a new deployment

### Testing Functions

Use these functions in the Apps Script editor to test functionality:

```javascript
// Test API access
function runTest() {
  const result = testSetup();
  console.log('Test results:', result);
  return result;
}

// Get sample event for testing
function findSampleEvent() {
  const sample = getSampleEvent();
  console.log('Sample event:', sample);
  return sample;
}

// Test processing a single event
function testSingleEvent() {
  const sample = getSampleEvent();
  if (sample.eventId) {
    const result = processEventIds(sample.eventId);
    console.log('Processing result:', result);
    return result;
  }
  return { error: 'No sample event found' };
}
```

## Security Considerations

### Access Control
- Set appropriate "Who has access" permissions
- Consider using "Anyone within [domain]" for organizational use
- Avoid "Anyone" unless absolutely necessary

### Data Privacy
- The application processes calendar and meeting data
- No data is permanently stored
- All processing happens in real-time
- Consider data retention policies for exported CSV files

### API Quotas
- Google APIs have usage quotas and rate limits
- The application includes delays between requests
- Monitor usage in the Google Cloud Console if needed

## Updating the Application

To update the deployed application:

1. Make changes to the code in Apps Script editor
2. Save the changes
3. Create a new deployment or update existing deployment
4. Test the updated functionality

## Monitoring and Maintenance

### Logs and Debugging
- Use Apps Script's execution transcript for debugging
- Enable verbose logging in the browser console
- Monitor API usage in Google Cloud Console

### Performance Optimization
- The application includes rate limiting for API calls
- Consider batch processing for large numbers of events
- Monitor execution time limits (Apps Script has 6-minute limit)

## Support and Resources

- [Google Apps Script Documentation](https://developers.google.com/apps-script)
- [Google Calendar API Documentation](https://developers.google.com/calendar/api)
- [Google Meet API Documentation](https://developers.google.com/meet/api)
- [Google Workspace Admin Console](https://admin.google.com) (for API management)

## Backup and Recovery

- Export your Apps Script project regularly
- Keep copies of all source files
- Document any custom configurations or modifications
- Consider version control for larger deployments
